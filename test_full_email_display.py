#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整邮件内容显示功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared_email_monitor import SharedEmailMonitor
from colorama import Fore, Style

def test_email_display():
    """测试邮件显示功能"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}📧 测试完整邮件内容显示功能{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    # 模拟邮件数据
    test_emails = [
        {
            'email_id': 'test001',
            'subject': 'Cursor 验证码',
            'from': '<EMAIL>',
            'date': 'Sat, 26 Jul 2025 15:30:00 +0800',
            'folder': 'inbox',
            'text_body': '''您好！

您的 Cursor 验证码是：123456

此验证码将在 10 分钟后过期，请尽快使用。

如果您没有请求此验证码，请忽略此邮件。

谢谢！
Cursor 团队''',
            'html_body': '''<html>
<body>
<h2>Cursor 验证码</h2>
<p>您好！</p>
<p>您的 Cursor 验证码是：<strong style="color: #007acc; font-size: 18px;">123456</strong></p>
<p>此验证码将在 10 分钟后过期，请尽快使用。</p>
<p>如果您没有请求此验证码，请忽略此邮件。</p>
<br>
<p>谢谢！<br>Cursor 团队</p>
</body>
</html>''',
            'full_body': '''您好！

您的 Cursor 验证码是：123456

此验证码将在 10 分钟后过期，请尽快使用。

如果您没有请求此验证码，请忽略此邮件。

谢谢！
Cursor 团队''',
            'verification_code': '123456'
        },
        {
            'email_id': 'test002',
            'subject': '[SPAM] GitHub 安全提醒',
            'from': '<EMAIL>',
            'date': 'Sat, 26 Jul 2025 14:20:00 +0800',
            'folder': 'junk',
            'text_body': '''安全提醒

检测到您的账户有异常登录活动。

验证码：789012

请立即验证您的身份。

GitHub 安全团队''',
            'html_body': '''<div style="font-family: Arial, sans-serif;">
<h3 style="color: #d73a49;">安全提醒</h3>
<p>检测到您的账户有异常登录活动。</p>
<div style="background: #f6f8fa; padding: 10px; border-left: 4px solid #d73a49;">
验证码：<span style="font-weight: bold; color: #d73a49;">789012</span>
</div>
<p>请立即验证您的身份。</p>
<p><em>GitHub 安全团队</em></p>
</div>''',
            'full_body': '''安全提醒

检测到您的账户有异常登录活动。

验证码：789012

请立即验证您的身份。

GitHub 安全团队''',
            'verification_code': '789012'
        },
        {
            'email_id': 'test003',
            'subject': '欢迎使用我们的服务',
            'from': '<EMAIL>',
            'date': 'Sat, 26 Jul 2025 13:10:00 +0800',
            'folder': 'inbox',
            'text_body': '''欢迎！

感谢您注册我们的服务。

这是一封欢迎邮件，没有验证码。

祝您使用愉快！''',
            'html_body': '''<html>
<body style="font-family: Arial, sans-serif;">
<h1 style="color: #4CAF50;">欢迎！</h1>
<p>感谢您注册我们的服务。</p>
<p>这是一封欢迎邮件，没有验证码。</p>
<p style="color: #666;">祝您使用愉快！</p>
</body>
</html>''',
            'full_body': '''欢迎！

感谢您注册我们的服务。

这是一封欢迎邮件，没有验证码。

祝您使用愉快！''',
            'verification_code': None
        }
    ]
    
    # 创建监听器实例（用于测试显示功能）
    monitor = SharedEmailMonitor(
        username="<EMAIL>",
        client_id="test_client_id",
        refresh_token="test_refresh_token"
    )
    
    # 测试每封邮件的显示
    for i, email_data in enumerate(test_emails, 1):
        print(f"\n{Fore.YELLOW}🧪 测试邮件 {i}/{len(test_emails)}{Style.RESET_ALL}")
        monitor.display_full_email_content(email_data)
        
        if i < len(test_emails):
            input(f"\n{Fore.CYAN}按回车键继续下一个测试...{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}✅ 所有测试完成！{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

if __name__ == "__main__":
    test_email_display()
