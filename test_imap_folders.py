#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试IMAP文件夹访问脚本
用于验证Outlook邮箱的文件夹结构和访问权限
"""

import imaplib
import requests
from colorama import Fore, Style

def test_imap_folders(username, client_id, refresh_token):
    """测试IMAP文件夹访问"""
    print(f"{Fore.CYAN}开始测试 {username} 的IMAP文件夹访问...{Style.RESET_ALL}")
    
    # 获取访问令牌
    try:
        headers = {
            'Host': 'login.microsoftonline.com',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        }
        data = {
            "client_id": client_id,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token",
        }

        token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
        response = requests.post(token_url, headers=headers, data=data, timeout=10)
        response.raise_for_status()
        token_data = response.json()

        if "access_token" not in token_data:
            print(f"{Fore.RED}❌ 获取访问令牌失败{Style.RESET_ALL}")
            return False
            
        access_token = token_data["access_token"]
        print(f"{Fore.GREEN}✅ 成功获取访问令牌{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ 获取访问令牌失败: {str(e)}{Style.RESET_ALL}")
        return False
    
    # 测试IMAP连接
    try:
        auth_string = f"user={username}\1auth=Bearer {access_token}\1\1"
        
        # 尝试连接IMAP服务器
        servers = [
            ("outlook.office365.com", "商业版"),
            ("imap-mail.outlook.com", "个人版")
        ]
        
        mail = None
        for server, server_type in servers:
            try:
                print(f"{Fore.CYAN}🔗 尝试连接 {server} ({server_type})...{Style.RESET_ALL}")
                mail = imaplib.IMAP4_SSL(server, timeout=15)
                mail.authenticate("XOAUTH2", lambda x: auth_string.encode())
                print(f"{Fore.GREEN}✅ 成功连接到 {server}{Style.RESET_ALL}")
                break
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️  连接 {server} 失败: {str(e)}{Style.RESET_ALL}")
                continue
        
        if not mail:
            print(f"{Fore.RED}❌ 无法连接到任何IMAP服务器{Style.RESET_ALL}")
            return False
        
        # 列出所有可用文件夹
        print(f"\n{Fore.CYAN}📁 列出所有可用文件夹:{Style.RESET_ALL}")
        status, folders = mail.list()
        if status == 'OK':
            for folder in folders:
                folder_str = folder.decode('utf-8')
                print(f"  {Fore.BLUE}{folder_str}{Style.RESET_ALL}")
        
        # 测试特定文件夹
        folders_to_test = ['INBOX', 'Junk', 'Spam', 'inbox', 'junk', 'spam']
        print(f"\n{Fore.CYAN}🔍 测试特定文件夹访问:{Style.RESET_ALL}")
        
        accessible_folders = []
        for folder in folders_to_test:
            try:
                status, _ = mail.select(folder)
                if status == 'OK':
                    status, messages = mail.search(None, 'ALL')
                    if status == 'OK':
                        email_count = len(messages[0].split()) if messages[0] else 0
                        print(f"  {Fore.GREEN}✅ {folder}: {email_count} 封邮件{Style.RESET_ALL}")
                        accessible_folders.append(folder)
                    else:
                        print(f"  {Fore.YELLOW}⚠️  {folder}: 可访问但搜索失败{Style.RESET_ALL}")
                else:
                    print(f"  {Fore.RED}❌ {folder}: 无法访问{Style.RESET_ALL}")
            except Exception as e:
                print(f"  {Fore.RED}❌ {folder}: 访问出错 - {str(e)}{Style.RESET_ALL}")
        
        mail.logout()
        
        print(f"\n{Fore.GREEN}📊 测试结果总结:{Style.RESET_ALL}")
        print(f"  可访问的文件夹: {accessible_folders}")
        
        return len(accessible_folders) > 0
        
    except Exception as e:
        print(f"{Fore.RED}❌ IMAP测试失败: {str(e)}{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    print("IMAP文件夹测试工具")
    print("=" * 50)
    
    # 这里需要用户提供测试参数
    username = input("请输入邮箱地址: ").strip()
    client_id = input("请输入Client ID: ").strip()
    refresh_token = input("请输入Refresh Token: ").strip()
    
    if username and client_id and refresh_token:
        test_imap_folders(username, client_id, refresh_token)
    else:
        print(f"{Fore.RED}❌ 请提供完整的测试参数{Style.RESET_ALL}")
