#!/bin/bash

# OAuth2令牌获取测试脚本
# 使用curl测试Microsoft OAuth2 API

echo "=== Microsoft OAuth2 令牌获取测试 ==="
echo

# 检查参数
if [ $# -ne 2 ]; then
    echo "用法: $0 <client_id> <refresh_token>"
    echo "示例: $0 your_client_id your_refresh_token"
    exit 1
fi

CLIENT_ID="$1"
REFRESH_TOKEN="$2"

echo "🔑 Client ID: ${CLIENT_ID:0:20}..."
echo "🔄 Refresh Token: ${REFRESH_TOKEN:0:30}..."
echo

# OAuth2令牌刷新请求
echo "📡 发送OAuth2令牌刷新请求..."
echo

RESPONSE=$(curl -s -X POST \
    -H "Host: login.microsoftonline.com" \
    -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" \
    -H "Content-Type: application/x-www-form-urlencoded;charset=UTF-8" \
    -d "client_id=${CLIENT_ID}" \
    -d "refresh_token=${REFRESH_TOKEN}" \
    -d "grant_type=refresh_token" \
    "https://login.microsoftonline.com/common/oauth2/v2.0/token")

echo "📥 API响应:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo

# 检查响应中是否包含access_token
if echo "$RESPONSE" | grep -q "access_token"; then
    echo "✅ 成功获取访问令牌"
    
    # 提取access_token
    ACCESS_TOKEN=$(echo "$RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('access_token', ''))
except:
    pass
" 2>/dev/null)
    
    if [ -n "$ACCESS_TOKEN" ]; then
        echo "🔐 Access Token: ${ACCESS_TOKEN:0:50}..."
        echo
        echo "🧪 可以使用此令牌测试IMAP连接"
        echo "   服务器: outlook.office365.com 或 imap-mail.outlook.com"
        echo "   端口: 993 (SSL)"
        echo "   认证: XOAUTH2"
    fi
else
    echo "❌ 获取访问令牌失败"
    
    # 检查错误信息
    if echo "$RESPONSE" | grep -q "error"; then
        ERROR=$(echo "$RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f\"错误: {data.get('error', 'unknown')}\")
    print(f\"描述: {data.get('error_description', 'no description')}\")
except:
    pass
" 2>/dev/null)
        echo "$ERROR"
    fi
fi

echo
echo "=== 测试完成 ==="
