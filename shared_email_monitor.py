#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享的邮箱监听模块
- 提供token缓存机制
- IMAP连接管理
- 邮件解析和验证码提取
- 用于菜单4和菜单5的邮箱监听功能
"""

import time
import requests
import imaplib
import email as email_module
from email.header import decode_header
import re

# 尝试导入colorama，如果不存在则使用空的Style和Fore
try:
    from colorama import Fore, Style
except ImportError:
    class MockColor:
        def __getattr__(self, name):
            return ""
    Fore = MockColor()
    Style = MockColor()

# EMOJI 字典
EMOJI = {
    "START": "🚀",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "WARNING": "⚠️",
    "INFO": "ℹ️",
    "WAIT": "⏳",
    "MAIL": "📧",
    "MONEY": "💰",
    "SETTINGS": "⚙️",
    "MONITOR": "📡",
    "STOP": "🛑"
}

class SharedEmailMonitor:
    """共享的邮箱监听类"""
    
    def __init__(self, username, client_id, refresh_token, proxies=None):
        self.username = username
        self.client_id = client_id
        self.refresh_token = refresh_token
        self.proxies = proxies
        self.seen_email_ids = set()
        
        # Token缓存机制
        self.cached_access_token = None
        self.token_cache_time = None
        self.token_cache_duration = 3600  # Token缓存1小时
        
    def get_access_token(self):
        """获取访问令牌 - 使用缓存机制避免重复获取"""
        # 检查是否有缓存的Token且未过期
        if self.cached_access_token and self.token_cache_time:
            current_time = time.time()
            if current_time - self.token_cache_time < self.token_cache_duration:
                return self.cached_access_token
        
        # 缓存过期或不存在，获取新Token
        print(f"{Fore.CYAN}{EMOJI['WAIT']} 正在获取新的访问令牌...{Style.RESET_ALL}")
        try:
            headers = {
                'Host': 'login.microsoftonline.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
            }
            data = {
                "client_id": self.client_id,
                "refresh_token": self.refresh_token,
                "grant_type": "refresh_token",
            }

            token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"

            response = requests.post(token_url, headers=headers, data=data, timeout=10, proxies=self.proxies)
            response.raise_for_status()
            token_data = response.json()

            if "access_token" in token_data:
                access_token = token_data["access_token"]
                new_refresh_token = token_data.get("refresh_token")
                
                # 缓存新Token
                self.cached_access_token = access_token
                self.token_cache_time = time.time()
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 新Token获取成功并已缓存{Style.RESET_ALL}")
                
                # 更新refresh_token
                if new_refresh_token and new_refresh_token != self.refresh_token:
                    self.refresh_token = new_refresh_token
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Refresh Token已更新{Style.RESET_ALL}")
                    
                return access_token
            else:
                error_msg = token_data.get("error_description", "获取access_token失败")
                print(f"{Fore.RED}{EMOJI['ERROR']} Token错误: {error_msg}{Style.RESET_ALL}")
                return None

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 获取访问令牌失败: {str(e)}{Style.RESET_ALL}")
            return None
    
    def connect_imap(self):
        """连接IMAP服务器"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return None, "无法获取访问令牌"

            # IMAP OAuth2认证
            auth_string = f"user={self.username}\1auth=Bearer {access_token}\1\1"

            # 首先尝试商业版IMAP服务器
            try:
                mail = imaplib.IMAP4_SSL("outlook.office365.com", timeout=15)
                mail.authenticate("XOAUTH2", lambda x: auth_string.encode())
                return mail, None
            except imaplib.IMAP4.error as e_office:
                # 尝试个人版IMAP服务器
                try:
                    mail_alt = imaplib.IMAP4_SSL("imap-mail.outlook.com", timeout=15)
                    mail_alt.authenticate("XOAUTH2", lambda x: auth_string.encode())
                    return mail_alt, None
                except imaplib.IMAP4.error as e_personal:
                    error_msg = f"IMAP认证失败: {e_office}"
                    return None, error_msg

        except Exception as e:
            error_msg = f"IMAP连接异常: {str(e)}"
            return None, error_msg
    
    def parse_email_details(self, mail, email_id):
        """解析邮件详情并提取验证码"""
        try:
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return None

            msg = email_module.message_from_bytes(msg_data[0][1])

            # 解析主题
            subject = msg['subject']
            if subject:
                decoded_subject = decode_header(subject)[0]
                if decoded_subject[1]:
                    subject = decoded_subject[0].decode(decoded_subject[1])
                else:
                    subject = decoded_subject[0]

            # 解析发件人
            from_addr = msg['from']

            # 解析日期
            date_str = msg['date']

            # 获取邮件正文 - 同时获取纯文本和HTML内容
            text_body = ""
            html_body = ""

            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()

                    if content_type == "text/plain" and not text_body:
                        try:
                            text_body = part.get_payload(decode=True).decode('utf-8')
                        except:
                            try:
                                text_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                            except:
                                continue

                    elif content_type == "text/html" and not html_body:
                        try:
                            html_body = part.get_payload(decode=True).decode('utf-8')
                        except:
                            try:
                                html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                            except:
                                continue
            else:
                content_type = msg.get_content_type()
                try:
                    content = msg.get_payload(decode=True).decode('utf-8')
                    if content_type == "text/plain":
                        text_body = content
                    elif content_type == "text/html":
                        html_body = content
                    else:
                        text_body = content  # 默认当作纯文本处理
                except:
                    try:
                        content = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                        text_body = content
                    except:
                        text_body = ""

            # 优先使用纯文本内容，如果没有则使用HTML内容
            full_body = text_body if text_body else html_body

            # 尝试提取验证码
            verification_code = None
            if full_body:
                # 匹配验证码的多种模式
                code_patterns = [
                    r'\b(\d{6})\b',  # 6位数字
                    r'\b(\d{4})\b',  # 4位数字
                    r'\b(\d{8})\b',  # 8位数字
                    r'code[:\s]*(\d{4,8})',  # "code: 123456"
                    r'verification[:\s]*(\d{4,8})',  # "verification: 123456"
                    r'verify[:\s]*(\d{4,8})',  # "verify: 123456"
                    r'pin[:\s]*(\d{4,8})',  # "pin: 123456"
                    r'otp[:\s]*(\d{4,8})',  # "otp: 123456"
                ]

                for pattern in code_patterns:
                    matches = re.findall(pattern, full_body, re.IGNORECASE)
                    if matches:
                        verification_code = matches[0]
                        break

            return {
                'email_id': email_id.decode(),
                'subject': subject,
                'from': from_addr,
                'date': date_str,
                'text_body': text_body,
                'html_body': html_body,
                'full_body': full_body,
                'body_preview': full_body[:200] + '...' if len(full_body) > 200 else full_body,
                'verification_code': verification_code
            }

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 解析邮件失败: {str(e)}{Style.RESET_ALL}")
            return None

    def display_full_email_content(self, email_details):
        """显示邮件的完整内容"""
        if not email_details:
            return

        folder_name = email_details.get('folder', 'unknown')
        folder_display = f"📁 {folder_name.upper()}"

        print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['MAIL']} 邮件完整内容 {folder_display}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

        # 基本信息
        print(f"{Fore.YELLOW}📧 主题: {Style.RESET_ALL}{email_details.get('subject', 'N/A')}")
        print(f"{Fore.YELLOW}📤 发件人: {Style.RESET_ALL}{email_details.get('from', 'N/A')}")
        print(f"{Fore.YELLOW}📅 日期: {Style.RESET_ALL}{email_details.get('date', 'N/A')}")
        print(f"{Fore.YELLOW}🆔 邮件ID: {Style.RESET_ALL}{email_details.get('email_id', 'N/A')}")

        # 验证码信息（如果有）
        if email_details.get('verification_code'):
            print(f"\n{Fore.GREEN}🎉 验证码: {Style.RESET_ALL}{Fore.YELLOW}{email_details['verification_code']}{Style.RESET_ALL}")
            if folder_name == 'junk':
                print(f"{Fore.RED}⚠️  注意：此验证码来自垃圾邮件文件夹{Style.RESET_ALL}")

        print(f"\n{Fore.CYAN}{'─'*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📝 邮件内容:{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'─'*80}{Style.RESET_ALL}")

        # 只显示纯文本内容，忽略空白行
        text_body = email_details.get('text_body', '')

        if text_body:
            # 按行分割，过滤空白行
            lines = text_body.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]

            if non_empty_lines:
                print(f"\n{Fore.WHITE}", end="")
                for line in non_empty_lines:
                    print(line)
                print(f"{Style.RESET_ALL}", end="")
            else:
                print(f"{Fore.YELLOW}⚠️  邮件内容为空{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}⚠️  邮件内容为空或无法解析{Style.RESET_ALL}")

        print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    def initialize_seen_emails(self):
        """初始化已见过的邮件ID列表，并显示最新邮件"""
        print(f"{Fore.YELLOW}{EMOJI['SETTINGS']} 正在初始化已有邮件列表...{Style.RESET_ALL}")

        mail, error = self.connect_imap()
        if error:
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化失败: {error}{Style.RESET_ALL}")
            return False

        try:
            # 检查多个文件夹：收件箱和垃圾邮件
            folders_to_check = ['inbox', 'junk']
            total_emails = 0
            latest_verification_code = None

            for folder in folders_to_check:
                try:
                    status, _ = mail.select(folder)
                    if status != 'OK':
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法访问文件夹: {folder}{Style.RESET_ALL}")
                        continue

                    status, messages = mail.search(None, 'ALL')
                    if status == 'OK':
                        email_ids = messages[0].split()

                        if email_ids:
                            print(f"{Fore.CYAN}{EMOJI['INFO']} 在 {folder} 文件夹找到 {len(email_ids)} 封邮件{Style.RESET_ALL}")

                            # 检查最新邮件是否包含验证码
                            latest_email_id = email_ids[-1]
                            latest_email_details = self.parse_email_details(mail, latest_email_id)

                            if latest_email_details:
                                # 添加文件夹信息
                                latest_email_details['folder'] = folder

                                if latest_email_details['verification_code']:
                                    latest_verification_code = latest_email_details['verification_code']
                                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 🎉 在 {folder} 文件夹发现现有验证码: {latest_verification_code}{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}📧 来自: {latest_email_details['subject']}{Style.RESET_ALL}")

                                    # 显示完整的邮件内容
                                    print(f"\n{Fore.YELLOW}📋 显示最新邮件的完整内容:{Style.RESET_ALL}")
                                    self.display_full_email_content(latest_email_details)

                            # 标记所有现有邮件为已见（添加文件夹前缀避免ID冲突）
                            for email_id in email_ids:
                                self.seen_email_ids.add(f"{folder}:{email_id.decode()}")

                            total_emails += len(email_ids)
                        else:
                            print(f"{Fore.BLUE}{EMOJI['INFO']} {folder} 文件夹为空{Style.RESET_ALL}")

                except Exception as folder_error:
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 检查文件夹 {folder} 时出错: {str(folder_error)}{Style.RESET_ALL}")
                    continue

            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 总共标记 {total_emails} 封现有邮件{Style.RESET_ALL}")
            mail.logout()
            return True

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化过程中出错: {str(e)}{Style.RESET_ALL}")
            mail.logout()
            return False
    
    def check_for_new_emails(self):
        """检查新邮件并返回验证码（如果找到）"""
        mail, error = self.connect_imap()
        if error:
            print(f"{Fore.RED}{EMOJI['ERROR']} 连接失败: {error}{Style.RESET_ALL}")
            return None

        try:
            # 检查多个文件夹：收件箱和垃圾邮件
            folders_to_check = ['inbox', 'junk']
            all_new_emails = []

            for folder in folders_to_check:
                try:
                    status, _ = mail.select(folder)
                    if status != 'OK':
                        continue  # 跳过无法访问的文件夹

                    status, messages = mail.search(None, 'ALL')
                    if status == 'OK':
                        email_ids = messages[0].split()

                        for email_id in email_ids:
                            email_id_str = f"{folder}:{email_id.decode()}"
                            if email_id_str not in self.seen_email_ids:
                                email_details = self.parse_email_details(mail, email_id)
                                if email_details:
                                    email_details['folder'] = folder  # 添加文件夹信息
                                    all_new_emails.append(email_details)
                                    self.seen_email_ids.add(email_id_str)

                except Exception as folder_error:
                    continue  # 跳过出错的文件夹

            mail.logout()

            if all_new_emails:
                for email_details in all_new_emails:
                    # 显示完整的邮件内容
                    self.display_full_email_content(email_details)

                    # 如果找到验证码，返回验证码（但仍然显示了完整内容）
                    if email_details.get('verification_code'):
                        return email_details['verification_code']

            return None

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 检查邮件时出错: {str(e)}{Style.RESET_ALL}")
            mail.logout()
            return None

    def monitor_for_verification_code(self, max_attempts=60, interval=5, continuous_mode=False):
        """持续监听验证码邮件
        
        Args:
            max_attempts: 最大监听次数 (continuous_mode=True时无效)
            interval: 监听间隔(秒)
            continuous_mode: 是否持续监听模式，即使找到验证码也不停止
        """
        print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['MAIL']} 开始监听 {self.username} 的验证码邮件{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        if continuous_mode:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 监听模式: 持续监听 (找到验证码后继续监听){Style.RESET_ALL}")
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 检查间隔: {interval} 秒 | 按 Ctrl+C 停止监听{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 监听设置: 最多 {max_attempts} 次，间隔 {interval} 秒{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 按 Ctrl+C 可以随时停止监听{Style.RESET_ALL}")
        
        print(f"{Fore.CYAN}{'-'*60}{Style.RESET_ALL}")
        
        # 初始化已有邮件
        if not self.initialize_seen_emails():
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化失败，无法继续监听{Style.RESET_ALL}")
            return None
        
        print(f"\n{Fore.CYAN}{EMOJI['MONITOR']} 开始监听新邮件...{Style.RESET_ALL}")
        
        try:
            attempt = 0
            monitoring = True
            first_verification_code = None  # 存储第一个找到的验证码
            
            while monitoring:
                if not continuous_mode and attempt >= max_attempts:
                    break
                    
                attempt += 1
                start_time = time.time()
                
                current_time = time.strftime("%H:%M:%S")
                if continuous_mode:
                    print(f"{Fore.CYAN}🔍 [{current_time}] 检查新邮件... (持续监听中){Style.RESET_ALL}", end='\r')
                else:
                    print(f"{Fore.CYAN}🔍 [{current_time}] 检查新邮件... ({attempt}/{max_attempts}){Style.RESET_ALL}", end='\r')
                
                verification_code_found = self.check_for_new_emails()
                
                if verification_code_found:
                    print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 🎉 成功获取验证码: {verification_code_found}{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
                    
                    if continuous_mode:
                        if first_verification_code is None:
                            first_verification_code = verification_code_found
                        print(f"{Fore.CYAN}{EMOJI['INFO']} 持续监听模式: 验证码已显示，继续监听新邮件...{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}{EMOJI['INFO']} 验证码已获取，请复制使用{Style.RESET_ALL}")
                        return verification_code_found
                
                # 精确控制检查间隔
                elapsed_time = time.time() - start_time
                sleep_time = max(0, interval - elapsed_time)
                
                if sleep_time > 0:
                    try:
                        time.sleep(sleep_time)
                    except KeyboardInterrupt:
                        print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 用户中断监听{Style.RESET_ALL}")
                        monitoring = False
                        break
            
            # 监听结束的提示
            if not monitoring:
                print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 监听已被用户中断{Style.RESET_ALL}")
                if continuous_mode and first_verification_code:
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 本次监听期间获取到的第一个验证码: {first_verification_code}{Style.RESET_ALL}")
                    return first_verification_code
            elif not continuous_mode:
                print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 监听结束，已达到最大尝试次数 ({max_attempts})，未收到验证码{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{EMOJI['INFO']} 建议检查:{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  1. 是否已发送验证码到该邮箱{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  2. 邮箱是否正常接收邮件{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  3. 网络连接是否正常{Style.RESET_ALL}")
            
            return first_verification_code if continuous_mode else None
            
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 监听被中断{Style.RESET_ALL}")
            if continuous_mode and first_verification_code:
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 本次监听期间获取到的第一个验证码: {first_verification_code}{Style.RESET_ALL}")
                return first_verification_code
            return None